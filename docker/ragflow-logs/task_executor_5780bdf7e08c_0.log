2025-08-11 16:45:09,244 INFO     33 task_executor_5780bdf7e08c_0 log path: /ragflow/logs/task_executor_5780bdf7e08c_0.log, log levels: {'peewee': 'WARNING', 'pdfminer': 'WARNING', 'root': 'INFO'}
2025-08-11 16:45:09,245 INFO     33 
  ______           __      ______                     __
 /_  __/___ ______/ /__   / ____/  _____  _______  __/ /_____  _____
  / / / __ `/ ___/ //_/  / __/ | |/_/ _ \/ ___/ / / / __/ __ \/ ___/
 / / / /_/ (__  ) ,<    / /____>  </  __/ /__/ /_/ / /_/ /_/ / /
/_/  \__,_/____/_/|_|  /_____/_/|_|\___/\___/\__,_/\__/\____/_/
    
2025-08-11 16:45:09,246 INFO     33 TaskExecutor: RAGFlow version: v0.19.0 full
2025-08-11 16:45:09,251 INFO     33 Use Elasticsearch http://es01:9200 as the doc engine.
2025-08-11 16:45:09,259 INFO     33 GET http://es01:9200/ [status:200 duration:0.006s]
2025-08-11 16:45:09,263 INFO     33 HEAD http://es01:9200/ [status:200 duration:0.004s]
2025-08-11 16:45:09,264 INFO     33 Elasticsearch http://es01:9200 is healthy.
2025-08-11 16:45:09,272 WARNING  33 Load term.freq FAIL!
2025-08-11 16:45:09,277 WARNING  33 Realtime synonym is disabled, since no redis connection.
2025-08-11 16:45:09,284 WARNING  33 Load term.freq FAIL!
2025-08-11 16:45:09,291 WARNING  33 Realtime synonym is disabled, since no redis connection.
2025-08-11 16:45:09,291 INFO     33 MAX_CONTENT_LENGTH: 134217728
2025-08-11 16:45:09,295 INFO     33 MAX_FILE_COUNT_PER_USER: 0
2025-08-11 16:45:09,308 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T16:45:09.306+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 16:45:09,311 INFO     33 task_executor_9a8d764f62de_0 expired, removed
2025-08-11 16:45:09,314 WARNING  33 RedisDB.get_unacked_iterator queue rag_flow_svr_queue_1 doesn't exist
2025-08-11 16:45:39,317 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T16:45:39.315+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 16:46:09,326 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T16:46:09.324+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 16:46:39,334 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T16:46:39.332+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 16:47:09,342 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T16:47:09.340+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 16:47:39,351 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T16:47:39.348+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 16:48:09,357 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T16:48:09.355+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 16:48:39,363 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T16:48:39.361+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 16:49:09,370 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T16:49:09.368+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 16:49:39,378 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T16:49:39.375+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 16:50:09,385 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T16:50:09.384+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 16:50:39,391 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T16:50:39.389+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 16:51:09,398 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T16:51:09.396+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 16:51:39,404 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T16:51:39.402+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 16:52:09,410 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T16:52:09.409+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 16:52:39,418 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T16:52:39.415+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 16:53:09,425 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T16:53:09.423+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 16:53:39,431 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T16:53:39.429+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 16:54:09,438 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T16:54:09.435+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 16:54:39,444 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T16:54:39.442+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 16:55:09,450 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T16:55:09.448+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 16:55:39,458 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T16:55:39.455+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 16:56:09,466 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T16:56:09.464+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 16:56:39,472 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T16:56:39.471+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 16:57:09,483 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T16:57:09.477+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 16:57:39,491 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T16:57:39.489+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 16:58:09,499 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T16:58:09.497+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 16:58:39,505 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T16:58:39.502+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 16:59:09,512 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T16:59:09.510+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 16:59:39,519 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T16:59:39.517+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:00:09,526 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:00:09.524+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:00:39,558 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:00:39.556+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:01:09,566 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:01:09.564+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:01:39,574 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:01:39.572+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:02:09,584 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:02:09.581+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:02:39,592 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:02:39.590+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:03:09,600 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:03:09.598+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:03:39,608 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:03:39.605+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:04:09,615 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:04:09.613+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:04:39,623 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:04:39.620+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:05:09,630 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:05:09.628+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:05:39,637 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:05:39.634+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:06:09,644 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:06:09.642+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:06:39,652 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:06:39.649+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:07:09,660 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:07:09.658+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:07:39,666 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:07:39.664+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:08:09,673 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:08:09.671+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:08:39,682 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:08:39.679+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:09:09,688 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:09:09.686+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:09:39,698 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:09:39.696+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:10:09,705 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:10:09.703+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:10:39,717 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:10:39.715+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:11:09,724 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:11:09.722+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:11:39,735 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:11:39.733+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:12:09,742 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:12:09.740+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:12:39,750 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:12:39.747+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:13:09,756 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:13:09.754+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:13:39,765 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:13:39.763+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:14:09,773 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:14:09.771+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:14:39,781 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:14:39.778+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:15:09,788 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:15:09.786+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:15:39,798 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:15:39.796+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:16:09,822 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:16:09.819+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:16:39,832 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:16:39.829+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:17:09,843 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:17:09.841+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:17:39,854 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:17:39.851+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:18:09,863 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:18:09.860+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:18:39,871 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:18:39.869+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:19:09,878 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:19:09.876+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:19:39,888 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:19:39.885+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:20:09,896 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:20:09.894+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:20:39,906 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:20:39.900+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:21:09,914 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:21:09.912+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:21:39,929 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:21:39.927+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:22:09,938 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:22:09.936+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:22:39,946 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:22:39.944+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:23:09,953 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:23:09.951+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:23:39,962 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:23:39.960+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:24:09,970 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:24:09.967+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:24:39,978 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:24:39.976+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:25:09,987 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:25:09.985+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:25:39,997 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:25:39.995+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:26:10,004 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:26:10.002+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:26:40,011 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:26:40.009+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:27:10,019 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:27:10.017+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:27:40,027 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:27:40.025+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:28:10,035 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:28:10.033+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:28:40,046 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:28:40.043+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:29:10,054 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:29:10.052+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:29:40,063 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:29:40.061+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:30:10,071 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:30:10.069+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:30:40,081 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:30:40.078+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:31:10,089 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:31:10.087+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:31:40,100 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:31:40.097+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:32:10,108 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:32:10.105+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:32:40,142 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:32:40.140+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:33:10,149 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:33:10.147+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:33:40,169 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:33:40.166+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:34:10,177 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:34:10.175+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:34:40,189 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:34:40.186+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:35:10,199 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:35:10.197+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:35:40,208 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:35:40.205+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:36:10,216 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:36:10.214+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:36:40,226 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:36:40.224+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:37:10,235 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:37:10.232+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:37:40,244 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:37:40.242+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:38:10,252 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:38:10.250+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:38:40,261 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:38:40.259+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:39:10,269 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:39:10.267+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:39:40,277 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:39:40.275+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:40:10,287 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:40:10.284+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:40:40,297 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:40:40.294+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:41:10,306 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:41:10.303+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:41:40,317 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:41:40.314+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:42:10,324 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:42:10.322+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:42:40,336 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:42:40.334+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:43:10,344 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:43:10.342+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:43:40,351 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:43:40.350+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:44:10,356 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:44:10.354+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:44:40,362 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:44:40.361+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:45:10,365 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:45:10.364+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:45:40,369 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:45:40.368+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:46:10,373 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:46:10.372+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:46:40,376 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:46:40.375+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:47:10,381 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:47:10.379+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:47:40,386 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:47:40.385+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:48:10,388 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:48:10.387+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:48:40,392 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:48:40.391+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:49:10,396 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:49:10.395+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:49:40,400 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:49:40.399+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:50:10,413 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:50:10.412+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:50:40,416 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:50:40.416+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:51:10,419 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:51:10.417+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:51:40,423 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:51:40.422+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:52:10,425 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:52:10.424+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:52:40,429 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:52:40.428+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:53:10,433 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:53:10.432+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:53:40,439 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:53:40.437+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:54:10,443 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:54:10.443+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:54:40,447 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:54:40.446+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:55:10,451 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:55:10.450+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:55:40,453 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:55:40.453+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:56:10,456 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:56:10.455+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:56:40,459 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:56:40.458+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:57:10,462 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:57:10.461+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:57:40,466 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:57:40.465+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:58:10,469 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:58:10.468+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:58:40,473 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:58:40.471+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:59:10,477 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:59:10.476+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 17:59:40,482 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T17:59:40.481+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 18:00:10,485 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:00:10.484+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 18:00:40,490 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:00:40.489+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 18:01:10,495 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:01:10.494+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 18:01:40,500 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:01:40.500+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 18:02:10,503 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:02:10.503+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 18:02:40,508 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:02:40.506+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 18:03:10,512 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:03:10.511+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 18:03:40,517 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:03:40.516+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 18:04:10,521 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:04:10.520+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 0, "failed": 0, "current": {}}
2025-08-11 18:04:40,525 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:04:40.525+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 3, "done": 0, "failed": 0, "current": {}}
2025-08-11 18:04:43,702 INFO     33 handle_task begin for task {"id": "98824510769a11f09e1e02420ae90a05", "doc_id": "ad116224506e11f0880302420ae90806", "from_page": 0, "to_page": 12, "retry_count": 0, "kb_id": "219ad39c506e11f0935102420ae90806", "parser_id": "naive", "parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 128, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "name": "30_7-7__WELL__30-07-07_PB-706-0543.pdf", "type": "pdf", "location": "30_7-7__WELL__30-07-07_PB-706-0543.pdf", "size": 4452863, "tenant_id": "ee27350e4bb411f0832102420ae90806", "language": "English", "embd_id": "Alibaba-NLP/gte-large-en-v1.5___OpenAI-API@OpenAI-API-Compatible", "pagerank": 0, "kb_parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 128, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "img2txt_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "asr_id": "", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "update_time": 1754906680426, "task_type": ""}
2025-08-11 18:04:44,003 INFO     33 HTTP Request: POST http://eoaagmld003:8111/v1/embeddings "HTTP/1.1 200 OK"
2025-08-11 18:04:44,021 INFO     33 HEAD http://es01:9200/ragflow_ee27350e4bb411f0832102420ae90806 [status:200 duration:0.005s]
2025-08-11 18:04:44,050 INFO     33 handle_task begin for task {"id": "98824682769a11f09e1e02420ae90a05", "doc_id": "ad116224506e11f0880302420ae90806", "from_page": 12, "to_page": 24, "retry_count": 0, "kb_id": "219ad39c506e11f0935102420ae90806", "parser_id": "naive", "parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 128, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "name": "30_7-7__WELL__30-07-07_PB-706-0543.pdf", "type": "pdf", "location": "30_7-7__WELL__30-07-07_PB-706-0543.pdf", "size": 4452863, "tenant_id": "ee27350e4bb411f0832102420ae90806", "language": "English", "embd_id": "Alibaba-NLP/gte-large-en-v1.5___OpenAI-API@OpenAI-API-Compatible", "pagerank": 0, "kb_parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 128, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "img2txt_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "asr_id": "", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "update_time": 1754906680427, "task_type": ""}
2025-08-11 18:04:44,244 INFO     33 HTTP Request: POST http://eoaagmld003:8111/v1/embeddings "HTTP/1.1 200 OK"
2025-08-11 18:04:44,260 INFO     33 HEAD http://es01:9200/ragflow_ee27350e4bb411f0832102420ae90806 [status:200 duration:0.005s]
2025-08-11 18:04:44,286 INFO     33 handle_task begin for task {"id": "988246f0769a11f09e1e02420ae90a05", "doc_id": "ad116224506e11f0880302420ae90806", "from_page": 24, "to_page": 28, "retry_count": 0, "kb_id": "219ad39c506e11f0935102420ae90806", "parser_id": "naive", "parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 128, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "name": "30_7-7__WELL__30-07-07_PB-706-0543.pdf", "type": "pdf", "location": "30_7-7__WELL__30-07-07_PB-706-0543.pdf", "size": 4452863, "tenant_id": "ee27350e4bb411f0832102420ae90806", "language": "English", "embd_id": "Alibaba-NLP/gte-large-en-v1.5___OpenAI-API@OpenAI-API-Compatible", "pagerank": 0, "kb_parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 128, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "img2txt_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "asr_id": "", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "update_time": 1754906680428, "task_type": ""}
2025-08-11 18:04:44,537 INFO     33 HTTP Request: POST http://eoaagmld003:8111/v1/embeddings "HTTP/1.1 200 OK"
2025-08-11 18:04:44,555 INFO     33 HEAD http://es01:9200/ragflow_ee27350e4bb411f0832102420ae90806 [status:200 duration:0.005s]
2025-08-11 18:04:44,701 INFO     33 From minio(0.679699175991118) 30_7-7__WELL__30-07-07_PB-706-0543.pdf/30_7-7__WELL__30-07-07_PB-706-0543.pdf
2025-08-11 18:04:44,702 INFO     33 From minio(0.44154469994828105) 30_7-7__WELL__30-07-07_PB-706-0543.pdf/30_7-7__WELL__30-07-07_PB-706-0543.pdf
2025-08-11 18:04:44,705 INFO     33 From minio(0.14924068609252572) 30_7-7__WELL__30-07-07_PB-706-0543.pdf/30_7-7__WELL__30-07-07_PB-706-0543.pdf
2025-08-11 18:04:44,720 INFO     33 set_progress(98824510769a11f09e1e02420ae90a05), progress: 0.1, progress_msg: 18:04:44 Page(1~13): Start to parse.
2025-08-11 18:04:44,720 INFO     33 load_model /ragflow/rag/res/deepdoc/det.onnx reuses cached model
2025-08-11 18:04:44,729 INFO     33 load_model /ragflow/rag/res/deepdoc/rec.onnx reuses cached model
2025-08-11 18:04:45,012 INFO     33 load_model /ragflow/rag/res/deepdoc/layout.onnx uses CPU
2025-08-11 18:04:45,101 INFO     33 load_model /ragflow/rag/res/deepdoc/tsr.onnx uses CPU
2025-08-11 18:04:45,102 DEBUG    33 [RAGFlowPdfParser] custom table transformer set: <deepdoc.parser.pdf_parser.CustomTableStructureRecognizer object at 0x7f320b5ee0b0>
2025-08-11 18:04:45,306 INFO     33 set_progress(98824510769a11f09e1e02420ae90a05), progress: 0.15, progress_msg: 18:04:45 Page(1~13): Visual model detected. Attempting to enhance figure extraction...
2025-08-11 18:04:45,313 INFO     33 set_progress(98824510769a11f09e1e02420ae90a05), progress: None, progress_msg: 18:04:45 Page(1~13): OCR started
2025-08-11 18:04:46,991 INFO     33 __images__ dedupe_chars cost 1.6784719310235232s
2025-08-11 18:04:47,201 INFO     33 __ocr detecting boxes of a image cost (0.19851802405901253s)
2025-08-11 18:04:47,202 INFO     33 __ocr sorting 0 chars cost 0.00021859200205653906s
2025-08-11 18:04:47,325 INFO     33 __ocr recognize 11 boxes cost 0.1190004339441657s
2025-08-11 18:04:47,423 INFO     33 __ocr detecting boxes of a image cost (0.09753970510791987s)
2025-08-11 18:04:47,424 INFO     33 __ocr sorting 0 chars cost 0.0003235629992559552s
2025-08-11 18:04:47,601 INFO     33 __ocr recognize 25 boxes cost 0.17540515190921724s
2025-08-11 18:04:47,708 INFO     33 __ocr detecting boxes of a image cost (0.10654604400042444s)
2025-08-11 18:04:47,709 INFO     33 __ocr sorting 0 chars cost 0.00027876405511051416s
2025-08-11 18:04:47,856 INFO     33 __ocr recognize 17 boxes cost 0.14630255696829408s
2025-08-11 18:04:47,975 INFO     33 __ocr detecting boxes of a image cost (0.11824052897281945s)
2025-08-11 18:04:47,976 INFO     33 __ocr sorting 0 chars cost 0.0007124559488147497s
2025-08-11 18:04:48,520 INFO     33 __ocr recognize 45 boxes cost 0.5401871950598434s
2025-08-11 18:04:48,646 INFO     33 __ocr detecting boxes of a image cost (0.1258007319411263s)
2025-08-11 18:04:48,648 INFO     33 __ocr sorting 0 chars cost 0.0005381889641284943s
2025-08-11 18:04:49,361 INFO     33 __ocr recognize 29 boxes cost 0.7120093409903347s
2025-08-11 18:04:49,492 INFO     33 __ocr detecting boxes of a image cost (0.12891461397521198s)
2025-08-11 18:04:49,493 INFO     33 __ocr sorting 0 chars cost 0.000745218014344573s
2025-08-11 18:04:50,177 INFO     33 __ocr recognize 40 boxes cost 0.683242711937055s
2025-08-11 18:04:50,186 INFO     33 set_progress(98824510769a11f09e1e02420ae90a05), progress: 0.3, progress_msg: 
2025-08-11 18:04:50,311 INFO     33 __ocr detecting boxes of a image cost (0.12430946307722479s)
2025-08-11 18:04:50,312 INFO     33 __ocr sorting 0 chars cost 0.0007206000154837966s
2025-08-11 18:04:50,963 INFO     33 __ocr recognize 36 boxes cost 0.6488552839728072s
2025-08-11 18:04:51,089 INFO     33 __ocr detecting boxes of a image cost (0.12605190998874605s)
2025-08-11 18:04:51,090 INFO     33 __ocr sorting 0 chars cost 0.0007379889721050858s
2025-08-11 18:04:51,804 INFO     33 __ocr recognize 38 boxes cost 0.7121647680178285s
2025-08-11 18:04:51,940 INFO     33 __ocr detecting boxes of a image cost (0.1346265870379284s)
2025-08-11 18:04:51,941 INFO     33 __ocr sorting 0 chars cost 0.001013652072288096s
2025-08-11 18:04:52,360 INFO     33 __ocr recognize 54 boxes cost 0.4181077310349792s
2025-08-11 18:04:52,489 INFO     33 __ocr detecting boxes of a image cost (0.1287000220036134s)
2025-08-11 18:04:52,491 INFO     33 __ocr sorting 0 chars cost 0.0005992889637127519s
2025-08-11 18:04:53,267 INFO     33 __ocr recognize 30 boxes cost 0.7749830529792234s
2025-08-11 18:04:53,420 INFO     33 __ocr detecting boxes of a image cost (0.15230072592385113s)
2025-08-11 18:04:53,421 INFO     33 __ocr sorting 0 chars cost 0.0011998540721833706s
2025-08-11 18:04:53,986 INFO     33 __ocr recognize 103 boxes cost 0.5636815880425274s
2025-08-11 18:04:54,127 INFO     33 __ocr detecting boxes of a image cost (0.14023205393459648s)
2025-08-11 18:04:54,129 INFO     33 __ocr sorting 0 chars cost 0.0013189839664846659s
2025-08-11 18:04:54,503 INFO     33 __ocr recognize 66 boxes cost 0.3739304840564728s
2025-08-11 18:04:54,528 INFO     33 set_progress(98824510769a11f09e1e02420ae90a05), progress: 0.6, progress_msg: 
2025-08-11 18:04:54,529 INFO     33 __images__ 12 pages cost 7.526500458014198s
2025-08-11 18:04:54,537 INFO     33 set_progress(98824510769a11f09e1e02420ae90a05), progress: None, progress_msg: 18:04:54 Page(1~13): OCR finished (9.23s)
2025-08-11 18:04:54,537 INFO     33 OCR(0~12): 9.23s
2025-08-11 18:04:55,463 DEBUG    33 YOLO postprocess: 11 boxes on page ?. First 3: [{'type': 'text', 'bbox': [778.7109375, 950.5914916992188, 1161.627197265625, 987.838134765625], 'score': 0.5366371870040894}, {'type': 'text', 'bbox': [255.6448974609375, 178.45333862304688, 670.8521728515625, 321.41986083984375], 'score': 0.37740182876586914}, {'type': 'text', 'bbox': [868.515869140625, 862.20751953125, 1068.3255615234375, 897.1547241210938], 'score': 0.36729031801223755}]
2025-08-11 18:04:55,804 DEBUG    33 YOLO postprocess: 5 boxes on page ?. First 3: [{'type': 'title', 'bbox': [547.1114501953125, 806.36669921875, 873.994384765625, 842.2852172851562], 'score': 0.12043642997741699}, {'type': 'text', 'bbox': [511.79742431640625, 905.8543090820312, 1225.9102783203125, 1423.96484375], 'score': 0.36727142333984375}, {'type': 'text', 'bbox': [547.1114501953125, 806.36669921875, 873.994384765625, 842.2852172851562], 'score': 0.28103089332580566}]
2025-08-11 18:04:56,119 DEBUG    33 YOLO postprocess: 11 boxes on page ?. First 3: [{'type': 'title', 'bbox': [732.83251953125, 703.1740112304688, 990.37890625, 767.595703125], 'score': 0.13932377099990845}, {'type': 'text', 'bbox': [534.898193359375, 875.40283203125, 1161.5386962890625, 1292.2919921875], 'score': 0.5871750116348267}, {'type': 'text', 'bbox': [732.83251953125, 703.1740112304688, 990.37890625, 767.595703125], 'score': 0.23839324712753296}]
2025-08-11 18:04:56,436 DEBUG    33 YOLO postprocess: 16 boxes on page ?. First 3: [{'type': 'title', 'bbox': [260.38104248046875, 501.25311279296875, 677.8052368164062, 540.8455810546875], 'score': 0.5712584257125854}, {'type': 'title', 'bbox': [263.29193115234375, 1604.8179931640625, 436.85528564453125, 1647.314453125], 'score': 0.4640384614467621}, {'type': 'title', 'bbox': [190.55836486816406, 1921.8699951171875, 743.4188842773438, 1963.343017578125], 'score': 0.3442463278770447}]
2025-08-11 18:04:56,754 DEBUG    33 YOLO postprocess: 11 boxes on page ?. First 3: [{'type': 'title', 'bbox': [270.3416748046875, 838.8611450195312, 492.7290344238281, 882.6447143554688], 'score': 0.6084244251251221}, {'type': 'text', 'bbox': [317.6644287109375, 517.25341796875, 1573.8798828125, 719.2223510742188], 'score': 0.9073034524917603}, {'type': 'text', 'bbox': [317.877197265625, 361.5089416503906, 1583.76904296875, 459.3466796875], 'score': 0.8545516729354858}]
2025-08-11 18:04:57,139 DEBUG    33 YOLO postprocess: 9 boxes on page ?. First 3: [{'type': 'text', 'bbox': [621.0299682617188, 1305.58251953125, 1665.08203125, 1667.8133544921875], 'score': 0.7851739525794983}, {'type': 'text', 'bbox': [306.2167053222656, 1151.5517578125, 1553.041748046875, 1248.129150390625], 'score': 0.7045251727104187}, {'type': 'text', 'bbox': [544.0123901367188, 1724.265625, 1679.6409912109375, 2139.326416015625], 'score': 0.6479827165603638}]
2025-08-11 18:04:57,431 DEBUG    33 YOLO postprocess: 8 boxes on page ?. First 3: [{'type': 'text', 'bbox': [467.8029479980469, 1441.6431884765625, 1681.48486328125, 2174.99951171875], 'score': 0.8001089096069336}, {'type': 'text', 'bbox': [443.8018493652344, 335.93499755859375, 1686.037109375, 649.5457153320312], 'score': 0.7530744075775146}, {'type': 'text', 'bbox': [315.9932861328125, 705.1226806640625, 1698.6351318359375, 1015.123046875], 'score': 0.5612928867340088}]
2025-08-11 18:04:57,728 DEBUG    33 YOLO postprocess: 8 boxes on page ?. First 3: [{'type': 'text', 'bbox': [629.1466064453125, 886.93408203125, 1659.3489990234375, 1196.2841796875], 'score': 0.83310866355896}, {'type': 'text', 'bbox': [617.9066162109375, 1724.9759521484375, 1682.740234375, 2245.629638671875], 'score': 0.7980591058731079}, {'type': 'text', 'bbox': [310.0209045410156, 1464.536865234375, 1629.6212158203125, 1665.5172119140625], 'score': 0.7588080763816833}]
2025-08-11 18:04:58,023 DEBUG    33 YOLO postprocess: 8 boxes on page ?. First 3: [{'type': 'title', 'bbox': [221.44532775878906, 493.0586853027344, 635.2420043945312, 534.3998413085938], 'score': 0.7171887755393982}, {'type': 'text', 'bbox': [264.0900573730469, 866.7079467773438, 1442.6571044921875, 1918.5750732421875], 'score': 0.679221510887146}, {'type': 'text', 'bbox': [271.16357421875, 590.9454956054688, 1507.0028076171875, 794.0160522460938], 'score': 0.6177079081535339}]
2025-08-11 18:04:58,335 DEBUG    33 YOLO postprocess: 23 boxes on page ?. First 3: [{'type': 'title', 'bbox': [255.08120727539062, 1321.968505859375, 616.7191162109375, 1363.4337158203125], 'score': 0.5525769591331482}, {'type': 'title', 'bbox': [337.050048828125, 1429.453125, 513.48193359375, 1469.1964111328125], 'score': 0.29583168029785156}, {'type': 'title', 'bbox': [313.166748046875, 1706.3355712890625, 805.9334106445312, 1747.001220703125], 'score': 0.25410187244415283}]
2025-08-11 18:04:58,651 DEBUG    33 YOLO postprocess: 4 boxes on page ?. First 3: [{'type': 'title', 'bbox': [1148.78662109375, 304.43743896484375, 1316.0152587890625, 337.73443603515625], 'score': 0.326997309923172}, {'type': 'reference', 'bbox': [2133.281005859375, 1593.9503173828125, 2299.420166015625, 1624.88037109375], 'score': 0.5787439346313477}, {'type': 'table', 'bbox': [127.160400390625, 384.37896728515625, 2357.6005859375, 1512.382568359375], 'score': 0.9745485782623291}]
2025-08-11 18:04:58,967 DEBUG    33 YOLO postprocess: 4 boxes on page ?. First 3: [{'type': 'title', 'bbox': [1136.8828125, 351.4720764160156, 1302.286376953125, 383.89105224609375], 'score': 0.25879302620887756}, {'type': 'text', 'bbox': [1136.8828125, 351.4720764160156, 1302.286376953125, 383.89105224609375], 'score': 0.12938332557678223}, {'type': 'table', 'bbox': [119.4920425415039, 430.7342529296875, 2209.41455078125, 1441.58349609375], 'score': 0.9745553731918335}]
2025-08-11 18:04:59,012 INFO     33 set_progress(98824510769a11f09e1e02420ae90a05), progress: 0.63, progress_msg: 18:04:59 Page(1~13): Layout analysis (4.46s)
2025-08-11 18:04:59,019 DEBUG    33 [CustomTT] processing 2 tables via REST API
2025-08-11 18:05:08,656 DEBUG    33 [CustomTT] crop #0 output keys: ['table_objects', 'html', 'csv', 'score', 'qc_results']
2025-08-11 18:05:08,656 DEBUG    33 [CustomTT] crop #0 converted 21 table_objects to 21 components
2025-08-11 18:05:08,661 DEBUG    33 [CustomTT] crop #0 → 21 components
2025-08-11 18:05:10,529 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:05:10.528+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 3, "lag": 0, "done": 0, "failed": 0, "current": {"98824510769a11f09e1e02420ae90a05": {"id": "98824510769a11f09e1e02420ae90a05", "doc_id": "ad116224506e11f0880302420ae90806", "from_page": 0, "to_page": 12, "retry_count": 0, "kb_id": "219ad39c506e11f0935102420ae90806", "parser_id": "naive", "parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 128, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "name": "30_7-7__WELL__30-07-07_PB-706-0543.pdf", "type": "pdf", "location": "30_7-7__WELL__30-07-07_PB-706-0543.pdf", "size": 4452863, "tenant_id": "ee27350e4bb411f0832102420ae90806", "language": "English", "embd_id": "Alibaba-NLP/gte-large-en-v1.5___OpenAI-API@OpenAI-API-Compatible", "pagerank": 0, "kb_parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 128, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "img2txt_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "asr_id": "", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "update_time": 1754906680426, "task_type": ""}, "98824682769a11f09e1e02420ae90a05": {"id": "98824682769a11f09e1e02420ae90a05", "doc_id": "ad116224506e11f0880302420ae90806", "from_page": 12, "to_page": 24, "retry_count": 0, "kb_id": "219ad39c506e11f0935102420ae90806", "parser_id": "naive", "parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 128, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "name": "30_7-7__WELL__30-07-07_PB-706-0543.pdf", "type": "pdf", "location": "30_7-7__WELL__30-07-07_PB-706-0543.pdf", "size": 4452863, "tenant_id": "ee27350e4bb411f0832102420ae90806", "language": "English", "embd_id": "Alibaba-NLP/gte-large-en-v1.5___OpenAI-API@OpenAI-API-Compatible", "pagerank": 0, "kb_parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 128, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "img2txt_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "asr_id": "", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "update_time": 1754906680427, "task_type": ""}, "988246f0769a11f09e1e02420ae90a05": {"id": "988246f0769a11f09e1e02420ae90a05", "doc_id": "ad116224506e11f0880302420ae90806", "from_page": 24, "to_page": 28, "retry_count": 0, "kb_id": "219ad39c506e11f0935102420ae90806", "parser_id": "naive", "parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 128, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "name": "30_7-7__WELL__30-07-07_PB-706-0543.pdf", "type": "pdf", "location": "30_7-7__WELL__30-07-07_PB-706-0543.pdf", "size": 4452863, "tenant_id": "ee27350e4bb411f0832102420ae90806", "language": "English", "embd_id": "Alibaba-NLP/gte-large-en-v1.5___OpenAI-API@OpenAI-API-Compatible", "pagerank": 0, "kb_parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 128, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "img2txt_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "asr_id": "", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "update_time": 1754906680428, "task_type": ""}}}
2025-08-11 18:05:17,425 DEBUG    33 [CustomTT] crop #1 output keys: ['table_objects', 'html', 'csv', 'score', 'qc_results']
2025-08-11 18:05:17,426 DEBUG    33 [CustomTT] crop #1 converted 11 table_objects to 11 components
2025-08-11 18:05:17,428 DEBUG    33 [CustomTT] crop #1 → 11 components
2025-08-11 18:05:17,431 DEBUG    33 [CustomTT] finished, total 2 crops
2025-08-11 18:05:17,470 INFO     33 set_progress(98824510769a11f09e1e02420ae90a05), progress: 0.65, progress_msg: 18:05:17 Page(1~13): Table analysis (18.44s)
2025-08-11 18:05:17,489 INFO     33 set_progress(98824510769a11f09e1e02420ae90a05), progress: 0.67, progress_msg: 18:05:17 Page(1~13): Text merged (0.00s)
2025-08-11 18:05:18,168 INFO     33 layouts cost: 32.8625011210097s
2025-08-11 18:05:18,177 INFO     33 set_progress(98824510769a11f09e1e02420ae90a05), progress: 0.5, progress_msg: 18:05:18 Page(1~13): Basic parsing complete. Proceeding with figure enhancement...
2025-08-11 18:05:18,209 INFO     33 set_progress(98824510769a11f09e1e02420ae90a05), progress: 0.8, progress_msg: 18:05:18 Page(1~13): Finish parsing.
2025-08-11 18:05:18,584 INFO     33 naive_merge(30_7-7__WELL__30-07-07_PB-706-0543.pdf): 0.3748067329870537
2025-08-11 18:05:18,585 INFO     33 Chunking(34.563233016990125) 30_7-7__WELL__30-07-07_PB-706-0543.pdf/30_7-7__WELL__30-07-07_PB-706-0543.pdf done
2025-08-11 18:05:18,693 INFO     33 set_progress(98824682769a11f09e1e02420ae90a05), progress: 0.1, progress_msg: 18:05:18 Page(13~25): Start to parse.
2025-08-11 18:05:18,700 INFO     33 load_model /ragflow/rag/res/deepdoc/det.onnx reuses cached model
2025-08-11 18:05:18,743 INFO     33 load_model /ragflow/rag/res/deepdoc/rec.onnx reuses cached model
2025-08-11 18:05:18,749 INFO     33 load_model /ragflow/rag/res/deepdoc/layout.onnx reuses cached model
2025-08-11 18:05:18,754 INFO     33 load_model /ragflow/rag/res/deepdoc/tsr.onnx reuses cached model
2025-08-11 18:05:18,761 DEBUG    33 [RAGFlowPdfParser] custom table transformer set: <deepdoc.parser.pdf_parser.CustomTableStructureRecognizer object at 0x7f3204999480>
2025-08-11 18:05:18,962 INFO     33 MINIO PUT(30_7-7__WELL__30-07-07_PB-706-0543.pdf) cost 0.374 s
2025-08-11 18:05:18,974 INFO     33 Build document 30_7-7__WELL__30-07-07_PB-706-0543.pdf: 34.95s
2025-08-11 18:05:18,990 INFO     33 set_progress(98824510769a11f09e1e02420ae90a05), progress: None, progress_msg: 18:05:18 Page(1~13): Generate 19 chunks
2025-08-11 18:05:19,021 INFO     33 HTTP Request: POST http://eoaagmld003:8111/v1/embeddings "HTTP/1.1 200 OK"
2025-08-11 18:05:19,136 INFO     33 HTTP Request: POST http://eoaagmld003:8111/v1/embeddings "HTTP/1.1 200 OK"
2025-08-11 18:05:19,144 INFO     33 set_progress(98824682769a11f09e1e02420ae90a05), progress: 0.15, progress_msg: 18:05:19 Page(13~25): Visual model detected. Attempting to enhance figure extraction...
2025-08-11 18:05:19,173 INFO     33 set_progress(98824682769a11f09e1e02420ae90a05), progress: None, progress_msg: 18:05:19 Page(13~25): OCR started
2025-08-11 18:05:19,232 INFO     33 set_progress(98824510769a11f09e1e02420ae90a05), progress: 0.7105263157894737, progress_msg: 
2025-08-11 18:05:19,289 INFO     33 HTTP Request: POST http://eoaagmld003:8111/v1/embeddings "HTTP/1.1 200 OK"
2025-08-11 18:05:19,316 INFO     33 set_progress(98824510769a11f09e1e02420ae90a05), progress: 0.8789473684210526, progress_msg: 
2025-08-11 18:05:19,319 INFO     33 Embedding chunks (0.33s)
2025-08-11 18:05:19,332 INFO     33 set_progress(98824510769a11f09e1e02420ae90a05), progress: None, progress_msg: 18:05:19 Page(1~13): Embedding chunks (0.33s)
2025-08-11 18:05:19,419 INFO     33 PUT http://es01:9200/ragflow_ee27350e4bb411f0832102420ae90806/_bulk?refresh=false&timeout=60s [status:200 duration:0.066s]
2025-08-11 18:05:19,436 INFO     33 set_progress(98824510769a11f09e1e02420ae90a05), progress: 0.8052631578947369, progress_msg: 
2025-08-11 18:05:19,507 INFO     33 PUT http://es01:9200/ragflow_ee27350e4bb411f0832102420ae90806/_bulk?refresh=false&timeout=60s [status:200 duration:0.049s]
2025-08-11 18:05:19,553 INFO     33 PUT http://es01:9200/ragflow_ee27350e4bb411f0832102420ae90806/_bulk?refresh=false&timeout=60s [status:200 duration:0.023s]
2025-08-11 18:05:19,676 INFO     33 PUT http://es01:9200/ragflow_ee27350e4bb411f0832102420ae90806/_bulk?refresh=false&timeout=60s [status:200 duration:0.102s]
2025-08-11 18:05:19,706 INFO     33 PUT http://es01:9200/ragflow_ee27350e4bb411f0832102420ae90806/_bulk?refresh=false&timeout=60s [status:200 duration:0.010s]
2025-08-11 18:05:19,711 INFO     33 Indexing doc(30_7-7__WELL__30-07-07_PB-706-0543.pdf), page(0-12), chunks(19), elapsed: 0.38
2025-08-11 18:05:19,735 INFO     33 set_progress(98824510769a11f09e1e02420ae90a05), progress: 1.0, progress_msg: 18:05:19 Page(1~13): Indexing done (0.39s). Task done (36.02s)
2025-08-11 18:05:19,736 INFO     33 Chunk doc(30_7-7__WELL__30-07-07_PB-706-0543.pdf), page(0-12), chunks(19), token(3241), elapsed:36.02
2025-08-11 18:05:19,739 INFO     33 handle_task done for task {"id": "98824510769a11f09e1e02420ae90a05", "doc_id": "ad116224506e11f0880302420ae90806", "from_page": 0, "to_page": 12, "retry_count": 0, "kb_id": "219ad39c506e11f0935102420ae90806", "parser_id": "naive", "parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 128, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "name": "30_7-7__WELL__30-07-07_PB-706-0543.pdf", "type": "pdf", "location": "30_7-7__WELL__30-07-07_PB-706-0543.pdf", "size": 4452863, "tenant_id": "ee27350e4bb411f0832102420ae90806", "language": "English", "embd_id": "Alibaba-NLP/gte-large-en-v1.5___OpenAI-API@OpenAI-API-Compatible", "pagerank": 0, "kb_parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 128, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "img2txt_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "asr_id": "", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "update_time": 1754906680426, "task_type": ""}
2025-08-11 18:05:22,897 INFO     33 __images__ dedupe_chars cost 3.7192309730453417s
2025-08-11 18:05:23,044 INFO     33 __ocr detecting boxes of a image cost (0.13516464992426336s)
2025-08-11 18:05:23,045 INFO     33 __ocr sorting 0 chars cost 0.0004926649853587151s
2025-08-11 18:05:23,532 INFO     33 __ocr recognize 27 boxes cost 0.4858260080218315s
2025-08-11 18:05:23,667 INFO     33 __ocr detecting boxes of a image cost (0.13433107105083764s)
2025-08-11 18:05:23,668 INFO     33 __ocr sorting 0 chars cost 0.0007688599871471524s
2025-08-11 18:05:24,236 INFO     33 __ocr recognize 44 boxes cost 0.5652581339236349s
2025-08-11 18:05:24,368 INFO     33 __ocr detecting boxes of a image cost (0.13081088301260024s)
2025-08-11 18:05:24,369 INFO     33 __ocr sorting 0 chars cost 0.0006985090440139174s
2025-08-11 18:05:25,118 INFO     33 __ocr recognize 32 boxes cost 0.7478517349809408s
2025-08-11 18:05:25,248 INFO     33 __ocr detecting boxes of a image cost (0.12935505202040076s)
2025-08-11 18:05:25,249 INFO     33 __ocr sorting 0 chars cost 0.0003825610037893057s
2025-08-11 18:05:25,847 INFO     33 __ocr recognize 21 boxes cost 0.5965397109976038s
2025-08-11 18:05:25,973 INFO     33 __ocr detecting boxes of a image cost (0.1251288210041821s)
2025-08-11 18:05:25,973 INFO     33 __ocr sorting 0 chars cost 0.00041716895066201687s
2025-08-11 18:05:26,112 INFO     33 __ocr recognize 23 boxes cost 0.13795443391427398s
2025-08-11 18:05:26,257 INFO     33 __ocr detecting boxes of a image cost (0.14400261198170483s)
2025-08-11 18:05:26,257 INFO     33 __ocr sorting 0 chars cost 8.125498425215483e-05s
2025-08-11 18:05:26,326 INFO     33 __ocr recognize 3 boxes cost 0.06705251499079168s
2025-08-11 18:05:26,335 INFO     33 set_progress(98824682769a11f09e1e02420ae90a05), progress: 0.3, progress_msg: 
2025-08-11 18:05:26,477 INFO     33 __ocr detecting boxes of a image cost (0.14161905797664076s)
2025-08-11 18:05:26,477 INFO     33 __ocr sorting 0 chars cost 6.633601151406765e-05s
2025-08-11 18:05:26,541 INFO     33 __ocr recognize 2 boxes cost 0.05976513400673866s
2025-08-11 18:05:26,905 INFO     33 __ocr detecting boxes of a image cost (0.36280769505538046s)
2025-08-11 18:05:26,905 INFO     33 __ocr sorting 0 chars cost 0.00026242504827678204s
2025-08-11 18:05:27,127 INFO     33 set_progress(98824682769a11f09e1e02420ae90a05), progress: -1, progress_msg: 18:05:27 Page(13~25): [ERROR]Internal server error while chunking: OpenCV(4.10.0) /io/opencv/modules/imgproc/src/imgwarp.cpp:1813: error: (-215:Assertion failed) dst.cols < SHRT_MAX && dst.rows < SHRT_MAX && src.cols < SHRT_MAX && src.rows < SHRT_MAX in function remap

2025-08-11 18:05:27,127 ERROR    33 Chunking 30_7-7__WELL__30-07-07_PB-706-0543.pdf/30_7-7__WELL__30-07-07_PB-706-0543.pdf got exception
Traceback (most recent call last):
  File "/ragflow/rag/svr/task_executor.py", line 257, in build_chunks
    cks = await trio.to_thread.run_sync(lambda: chunker.chunk(task["name"], binary=binary, from_page=task["from_page"],
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 447, in to_thread_run_sync
    return msg_from_thread.unwrap()
  File "/ragflow/.venv/lib/python3.10/site-packages/outcome/_impl.py", line 213, in unwrap
    raise captured_error
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 373, in do_release_then_return_result
    return result.unwrap()
  File "/ragflow/.venv/lib/python3.10/site-packages/outcome/_impl.py", line 213, in unwrap
    raise captured_error
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 392, in worker_fn
    ret = context.run(sync_fn, *args)
  File "/ragflow/rag/svr/task_executor.py", line 257, in <lambda>
    cks = await trio.to_thread.run_sync(lambda: chunker.chunk(task["name"], binary=binary, from_page=task["from_page"],
  File "/ragflow/rag/app/naive.py", line 436, in chunk
    sections, tables, figures = pdf_parser(filename if not binary else binary, from_page=from_page, to_page=to_page, callback=callback, separate_tables_figures=True)
  File "/ragflow/rag/app/naive.py", line 256, in __call__
    self.__images__(
  File "/ragflow/deepdoc/parser/pdf_parser.py", line 1221, in __images__
    trio.run(__img_ocr_launcher)
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_core/_run.py", line 2423, in run
    raise runner.main_task_outcome.error
  File "/ragflow/deepdoc/parser/pdf_parser.py", line 1217, in __img_ocr_launcher
    await __img_ocr(i, 0, img, chars, None)
  File "/ragflow/deepdoc/parser/pdf_parser.py", line 1189, in __img_ocr
    self.__ocr(i + 1, img, chars, zoomin, id)
  File "<@beartype(deepdoc.parser.pdf_parser.RAGFlowPdfParser.__ocr) at 0x7f332beae290>", line 35, in __ocr
  File "/ragflow/deepdoc/parser/pdf_parser.py", line 459, in __ocr
    b["box_image"] = self.ocr.get_rotate_crop_image(img_np, np.array([[left, top], [right, top], [right, bott], [left, bott]], dtype=np.float32))
  File "/ragflow/deepdoc/vision/ocr.py", line 585, in get_rotate_crop_image
    dst_img = cv2.warpPerspective(
cv2.error: OpenCV(4.10.0) /io/opencv/modules/imgproc/src/imgwarp.cpp:1813: error: (-215:Assertion failed) dst.cols < SHRT_MAX && dst.rows < SHRT_MAX && src.cols < SHRT_MAX && src.rows < SHRT_MAX in function 'remap'

2025-08-11 18:05:27,160 INFO     33 set_progress(98824682769a11f09e1e02420ae90a05), progress: -1, progress_msg: 18:05:27 [ERROR][Exception]: OpenCV(4.10.0) /io/opencv/modules/imgproc/src/imgwarp.cpp:1813: error: (-215:Assertion failed) dst.cols < SHRT_MAX && dst.rows < SHRT_MAX && src.cols < SHRT_MAX && src.rows < SHRT_MAX in function 'remap'

2025-08-11 18:05:27,160 ERROR    33 handle_task got exception for task {"id": "98824682769a11f09e1e02420ae90a05", "doc_id": "ad116224506e11f0880302420ae90806", "from_page": 12, "to_page": 24, "retry_count": 0, "kb_id": "219ad39c506e11f0935102420ae90806", "parser_id": "naive", "parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 128, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "name": "30_7-7__WELL__30-07-07_PB-706-0543.pdf", "type": "pdf", "location": "30_7-7__WELL__30-07-07_PB-706-0543.pdf", "size": 4452863, "tenant_id": "ee27350e4bb411f0832102420ae90806", "language": "English", "embd_id": "Alibaba-NLP/gte-large-en-v1.5___OpenAI-API@OpenAI-API-Compatible", "pagerank": 0, "kb_parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 128, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "img2txt_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "asr_id": "", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "update_time": 1754906680427, "task_type": ""}
Traceback (most recent call last):
  File "/ragflow/rag/svr/task_executor.py", line 620, in handle_task
    await do_handle_task(task)
  File "/ragflow/rag/svr/task_executor.py", line 553, in do_handle_task
    chunks = await build_chunks(task, progress_callback)
  File "/ragflow/rag/svr/task_executor.py", line 257, in build_chunks
    cks = await trio.to_thread.run_sync(lambda: chunker.chunk(task["name"], binary=binary, from_page=task["from_page"],
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 447, in to_thread_run_sync
    return msg_from_thread.unwrap()
  File "/ragflow/.venv/lib/python3.10/site-packages/outcome/_impl.py", line 213, in unwrap
    raise captured_error
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 373, in do_release_then_return_result
    return result.unwrap()
  File "/ragflow/.venv/lib/python3.10/site-packages/outcome/_impl.py", line 213, in unwrap
    raise captured_error
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 392, in worker_fn
    ret = context.run(sync_fn, *args)
  File "/ragflow/rag/svr/task_executor.py", line 257, in <lambda>
    cks = await trio.to_thread.run_sync(lambda: chunker.chunk(task["name"], binary=binary, from_page=task["from_page"],
  File "/ragflow/rag/app/naive.py", line 436, in chunk
    sections, tables, figures = pdf_parser(filename if not binary else binary, from_page=from_page, to_page=to_page, callback=callback, separate_tables_figures=True)
  File "/ragflow/rag/app/naive.py", line 256, in __call__
    self.__images__(
  File "/ragflow/deepdoc/parser/pdf_parser.py", line 1221, in __images__
    trio.run(__img_ocr_launcher)
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_core/_run.py", line 2423, in run
    raise runner.main_task_outcome.error
  File "/ragflow/deepdoc/parser/pdf_parser.py", line 1217, in __img_ocr_launcher
    await __img_ocr(i, 0, img, chars, None)
  File "/ragflow/deepdoc/parser/pdf_parser.py", line 1189, in __img_ocr
    self.__ocr(i + 1, img, chars, zoomin, id)
  File "<@beartype(deepdoc.parser.pdf_parser.RAGFlowPdfParser.__ocr) at 0x7f332beae290>", line 35, in __ocr
  File "/ragflow/deepdoc/parser/pdf_parser.py", line 459, in __ocr
    b["box_image"] = self.ocr.get_rotate_crop_image(img_np, np.array([[left, top], [right, top], [right, bott], [left, bott]], dtype=np.float32))
  File "/ragflow/deepdoc/vision/ocr.py", line 585, in get_rotate_crop_image
    dst_img = cv2.warpPerspective(
cv2.error: OpenCV(4.10.0) /io/opencv/modules/imgproc/src/imgwarp.cpp:1813: error: (-215:Assertion failed) dst.cols < SHRT_MAX && dst.rows < SHRT_MAX && src.cols < SHRT_MAX && src.rows < SHRT_MAX in function 'remap'

2025-08-11 18:05:27,212 INFO     33 set_progress(988246f0769a11f09e1e02420ae90a05), progress: 0.1, progress_msg: 18:05:27 Page(25~29): Start to parse.
2025-08-11 18:05:27,212 INFO     33 load_model /ragflow/rag/res/deepdoc/det.onnx reuses cached model
2025-08-11 18:05:27,228 INFO     33 load_model /ragflow/rag/res/deepdoc/rec.onnx reuses cached model
2025-08-11 18:05:27,229 INFO     33 load_model /ragflow/rag/res/deepdoc/layout.onnx reuses cached model
2025-08-11 18:05:27,232 INFO     33 load_model /ragflow/rag/res/deepdoc/tsr.onnx reuses cached model
2025-08-11 18:05:27,235 DEBUG    33 [RAGFlowPdfParser] custom table transformer set: <deepdoc.parser.pdf_parser.CustomTableStructureRecognizer object at 0x7f32049f80d0>
2025-08-11 18:05:27,430 INFO     33 set_progress(988246f0769a11f09e1e02420ae90a05), progress: 0.15, progress_msg: 18:05:27 Page(25~29): Visual model detected. Attempting to enhance figure extraction...
2025-08-11 18:05:27,436 INFO     33 set_progress(988246f0769a11f09e1e02420ae90a05), progress: None, progress_msg: 18:05:27 Page(25~29): OCR started
2025-08-11 18:05:27,939 INFO     33 __images__ dedupe_chars cost 0.5020733770215884s
2025-08-11 18:05:28,127 INFO     33 __ocr detecting boxes of a image cost (0.17880408000200987s)
2025-08-11 18:05:28,129 INFO     33 __ocr sorting 0 chars cost 0.0016601609531790018s
2025-08-11 18:05:29,730 INFO     33 __ocr recognize 149 boxes cost 1.6003834110451862s
2025-08-11 18:05:29,907 INFO     33 __ocr detecting boxes of a image cost (0.1753840489545837s)
2025-08-11 18:05:29,908 INFO     33 __ocr sorting 0 chars cost 0.0008621930610388517s
2025-08-11 18:05:30,817 INFO     33 __ocr recognize 86 boxes cost 0.9056075959233567s
2025-08-11 18:05:30,973 INFO     33 __ocr detecting boxes of a image cost (0.15514588099904358s)
2025-08-11 18:05:30,974 INFO     33 __ocr sorting 0 chars cost 0.0008709910325706005s
2025-08-11 18:05:31,657 INFO     33 __ocr recognize 74 boxes cost 0.6819664719514549s
2025-08-11 18:05:31,817 INFO     33 __ocr detecting boxes of a image cost (0.15930954599753022s)
2025-08-11 18:05:31,818 INFO     33 __ocr sorting 0 chars cost 0.000621544080786407s
2025-08-11 18:05:32,308 INFO     33 __ocr recognize 39 boxes cost 0.4869084020610899s
2025-08-11 18:05:32,309 INFO     33 __images__ 4 pages cost 4.361269208020531s
2025-08-11 18:05:32,318 INFO     33 set_progress(988246f0769a11f09e1e02420ae90a05), progress: None, progress_msg: 18:05:32 Page(25~29): OCR finished (4.88s)
2025-08-11 18:05:32,318 INFO     33 OCR(24~28): 4.89s
2025-08-11 18:05:33,011 DEBUG    33 YOLO postprocess: 4 boxes on page ?. First 3: [{'type': 'reference', 'bbox': [77.58708953857422, 2423.031005859375, 312.3218994140625, 2448.27685546875], 'score': 0.5927169919013977}, {'type': 'reference', 'bbox': [1449.819580078125, 2377.2353515625, 1718.90771484375, 2427.52587890625], 'score': 0.29428184032440186}, {'type': 'figure', 'bbox': [26.8430118560791, 197.17745971679688, 1715.281494140625, 2429.195556640625], 'score': 0.10803079605102539}]
2025-08-11 18:05:33,470 DEBUG    33 YOLO postprocess: 5 boxes on page ?. First 3: [{'type': 'text', 'bbox': [1462.527099609375, 69.15612030029297, 1565.50048828125, 121.60993957519531], 'score': 0.5539160370826721}, {'type': 'reference', 'bbox': [1244.3375244140625, 2399.94775390625, 1394.2000732421875, 2429.899658203125], 'score': 0.737552285194397}, {'type': 'reference', 'bbox': [40.414878845214844, 2464.5810546875, 349.0305480957031, 2499.26220703125], 'score': 0.411406546831131}]
2025-08-11 18:05:33,969 DEBUG    33 YOLO postprocess: 5 boxes on page ?. First 3: [{'type': 'text', 'bbox': [259.69598388671875, 709.17578125, 317.90997314453125, 738.3750610351562], 'score': 0.09793221950531006}, {'type': 'reference', 'bbox': [831.7951049804688, 2428.0908203125, 1146.5628662109375, 2460.2255859375], 'score': 0.6503189206123352}, {'type': 'reference', 'bbox': [56.00764083862305, 2485.677490234375, 289.42901611328125, 2499.559326171875], 'score': 0.4099080562591553}]
2025-08-11 18:05:34,434 DEBUG    33 YOLO postprocess: 4 boxes on page ?. First 3: [{'type': 'reference', 'bbox': [2396.620849609375, 1677.268798828125, 2413.25390625, 1696.9886474609375], 'score': 0.18645963072776794}, {'type': 'figure', 'bbox': [115.97129821777344, 24.047237396240234, 2418.1044921875, 1665.4068603515625], 'score': 0.7460116147994995}, {'type': 'figure', 'bbox': [1628.5849609375, 1247.0054931640625, 2337.36962890625, 1636.0361328125], 'score': 0.18379735946655273}]
2025-08-11 18:05:34,462 INFO     33 set_progress(988246f0769a11f09e1e02420ae90a05), progress: 0.63, progress_msg: 18:05:34 Page(25~29): Layout analysis (2.13s)
2025-08-11 18:05:34,482 DEBUG    33 [CustomTT] processing 4 tables via REST API
2025-08-11 18:05:40,533 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:05:40.532+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 1, "lag": 0, "done": 1, "failed": 1, "current": {"988246f0769a11f09e1e02420ae90a05": {"id": "988246f0769a11f09e1e02420ae90a05", "doc_id": "ad116224506e11f0880302420ae90806", "from_page": 24, "to_page": 28, "retry_count": 0, "kb_id": "219ad39c506e11f0935102420ae90806", "parser_id": "naive", "parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 128, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "name": "30_7-7__WELL__30-07-07_PB-706-0543.pdf", "type": "pdf", "location": "30_7-7__WELL__30-07-07_PB-706-0543.pdf", "size": 4452863, "tenant_id": "ee27350e4bb411f0832102420ae90806", "language": "English", "embd_id": "Alibaba-NLP/gte-large-en-v1.5___OpenAI-API@OpenAI-API-Compatible", "pagerank": 0, "kb_parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 128, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "img2txt_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "asr_id": "", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "update_time": 1754906680428, "task_type": ""}}}
2025-08-11 18:05:44,122 DEBUG    33 [CustomTT] crop #0 output keys: ['table_objects', 'html', 'csv', 'score', 'qc_results']
2025-08-11 18:05:44,122 DEBUG    33 [CustomTT] crop #0 converted 11 table_objects to 11 components
2025-08-11 18:05:44,124 DEBUG    33 [CustomTT] crop #0 → 11 components
2025-08-11 18:05:56,772 DEBUG    33 [CustomTT] crop #1 output keys: ['table_objects', 'html', 'csv', 'score', 'qc_results']
2025-08-11 18:05:56,773 DEBUG    33 [CustomTT] crop #1 converted 147 table_objects to 147 components
2025-08-11 18:05:56,775 DEBUG    33 [CustomTT] crop #1 → 147 components
2025-08-11 18:06:10,537 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:06:10.535+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 1, "lag": 0, "done": 1, "failed": 1, "current": {"988246f0769a11f09e1e02420ae90a05": {"id": "988246f0769a11f09e1e02420ae90a05", "doc_id": "ad116224506e11f0880302420ae90806", "from_page": 24, "to_page": 28, "retry_count": 0, "kb_id": "219ad39c506e11f0935102420ae90806", "parser_id": "naive", "parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 128, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "name": "30_7-7__WELL__30-07-07_PB-706-0543.pdf", "type": "pdf", "location": "30_7-7__WELL__30-07-07_PB-706-0543.pdf", "size": 4452863, "tenant_id": "ee27350e4bb411f0832102420ae90806", "language": "English", "embd_id": "Alibaba-NLP/gte-large-en-v1.5___OpenAI-API@OpenAI-API-Compatible", "pagerank": 0, "kb_parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 128, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "img2txt_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "asr_id": "", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "update_time": 1754906680428, "task_type": ""}}}
2025-08-11 18:06:10,766 DEBUG    33 [CustomTT] crop #2 output keys: ['table_objects', 'html', 'csv', 'score', 'qc_results']
2025-08-11 18:06:10,767 DEBUG    33 [CustomTT] crop #2 converted 159 table_objects to 159 components
2025-08-11 18:06:10,769 DEBUG    33 [CustomTT] crop #2 → 159 components
2025-08-11 18:06:19,086 DEBUG    33 [CustomTT] crop #3 output keys: ['table_objects', 'html', 'csv', 'score', 'qc_results']
2025-08-11 18:06:19,087 DEBUG    33 [CustomTT] crop #3 converted 21 table_objects to 21 components
2025-08-11 18:06:19,094 DEBUG    33 [CustomTT] crop #3 → 21 components
2025-08-11 18:06:19,101 DEBUG    33 [CustomTT] finished, total 4 crops
2025-08-11 18:06:19,120 INFO     33 set_progress(988246f0769a11f09e1e02420ae90a05), progress: -1, progress_msg: 18:06:19 Page(25~29): [ERROR]Internal server error while chunking: Bbox mismatch! T:1954.2252909342449,B:1951.8919576009116,X0:21.90475845336914,X1:522.2380917867025 => {x0: 21.90475845336914, x1: 522.2380917867025, top: 1945.2252909342449, bottom: 1968.2252909342449, label: table row, score: 0.7636525630950928, pn: 2, layoutno: 0}
2025-08-11 18:06:19,120 ERROR    33 Chunking 30_7-7__WELL__30-07-07_PB-706-0543.pdf/30_7-7__WELL__30-07-07_PB-706-0543.pdf got exception
Traceback (most recent call last):
  File "/ragflow/rag/svr/task_executor.py", line 257, in build_chunks
    cks = await trio.to_thread.run_sync(lambda: chunker.chunk(task["name"], binary=binary, from_page=task["from_page"],
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 447, in to_thread_run_sync
    return msg_from_thread.unwrap()
  File "/ragflow/.venv/lib/python3.10/site-packages/outcome/_impl.py", line 213, in unwrap
    raise captured_error
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 373, in do_release_then_return_result
    return result.unwrap()
  File "/ragflow/.venv/lib/python3.10/site-packages/outcome/_impl.py", line 213, in unwrap
    raise captured_error
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 392, in worker_fn
    ret = context.run(sync_fn, *args)
  File "/ragflow/rag/svr/task_executor.py", line 257, in <lambda>
    cks = await trio.to_thread.run_sync(lambda: chunker.chunk(task["name"], binary=binary, from_page=task["from_page"],
  File "/ragflow/rag/app/naive.py", line 436, in chunk
    sections, tables, figures = pdf_parser(filename if not binary else binary, from_page=from_page, to_page=to_page, callback=callback, separate_tables_figures=True)
  File "/ragflow/rag/app/naive.py", line 271, in __call__
    self._table_transformer_job(zoomin)
  File "/ragflow/deepdoc/parser/pdf_parser.py", line 369, in _table_transformer_job
    rows = gather(r".* (row|header)")
  File "/ragflow/deepdoc/parser/pdf_parser.py", line 364, in gather
    eles = Recognizer.layouts_cleanup(self.boxes, eles, 5, ption)
  File "/ragflow/deepdoc/vision/recognizer.py", line 152, in layouts_cleanup
    if Recognizer.overlapped_area(layouts[i], layouts[j]) < thr \
  File "/ragflow/deepdoc/vision/recognizer.py", line 126, in overlapped_area
    assert tp_ <= btm_, "Bbox mismatch! T:{},B:{},X0:{},X1:{} => {}".format(
AssertionError: Bbox mismatch! T:1954.2252909342449,B:1951.8919576009116,X0:21.90475845336914,X1:522.2380917867025 => {'x0': 21.90475845336914, 'x1': 522.2380917867025, 'top': 1945.2252909342449, 'bottom': 1968.2252909342449, 'label': 'table row', 'score': 0.7636525630950928, 'pn': 2, 'layoutno': 0}
2025-08-11 18:06:19,130 INFO     33 set_progress(988246f0769a11f09e1e02420ae90a05), progress: -1, progress_msg: 18:06:19 [ERROR][Exception]: Bbox mismatch! T:1954.2252909342449,B:1951.8919576009116,X0:21.90475845336914,X1:522.2380917867025 => {'x0': 21.90475845336914, 'x1': 522.2380917867025, 'top': 1945.2252909342449, 'bottom': 1968.2252909342449, 'label': 'table row', 'score': 0.7636525630950928, 'pn': 2, 'layoutno': 0}
2025-08-11 18:06:19,130 ERROR    33 handle_task got exception for task {"id": "988246f0769a11f09e1e02420ae90a05", "doc_id": "ad116224506e11f0880302420ae90806", "from_page": 24, "to_page": 28, "retry_count": 0, "kb_id": "219ad39c506e11f0935102420ae90806", "parser_id": "naive", "parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 128, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "name": "30_7-7__WELL__30-07-07_PB-706-0543.pdf", "type": "pdf", "location": "30_7-7__WELL__30-07-07_PB-706-0543.pdf", "size": 4452863, "tenant_id": "ee27350e4bb411f0832102420ae90806", "language": "English", "embd_id": "Alibaba-NLP/gte-large-en-v1.5___OpenAI-API@OpenAI-API-Compatible", "pagerank": 0, "kb_parser_config": {"layout_recognize": "DeepDOC", "chunk_token_num": 128, "delimiter": "\n", "auto_keywords": 0, "auto_questions": 0, "html4excel": false, "raptor": {"use_raptor": false}, "graphrag": {"use_graphrag": false}}, "img2txt_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "asr_id": "", "llm_id": "Qwen/Qwen2.5-VL-72B-Instruct___VLLM@VLLM", "update_time": 1754906680428, "task_type": ""}
Traceback (most recent call last):
  File "/ragflow/rag/svr/task_executor.py", line 620, in handle_task
    await do_handle_task(task)
  File "/ragflow/rag/svr/task_executor.py", line 553, in do_handle_task
    chunks = await build_chunks(task, progress_callback)
  File "/ragflow/rag/svr/task_executor.py", line 257, in build_chunks
    cks = await trio.to_thread.run_sync(lambda: chunker.chunk(task["name"], binary=binary, from_page=task["from_page"],
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 447, in to_thread_run_sync
    return msg_from_thread.unwrap()
  File "/ragflow/.venv/lib/python3.10/site-packages/outcome/_impl.py", line 213, in unwrap
    raise captured_error
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 373, in do_release_then_return_result
    return result.unwrap()
  File "/ragflow/.venv/lib/python3.10/site-packages/outcome/_impl.py", line 213, in unwrap
    raise captured_error
  File "/ragflow/.venv/lib/python3.10/site-packages/trio/_threads.py", line 392, in worker_fn
    ret = context.run(sync_fn, *args)
  File "/ragflow/rag/svr/task_executor.py", line 257, in <lambda>
    cks = await trio.to_thread.run_sync(lambda: chunker.chunk(task["name"], binary=binary, from_page=task["from_page"],
  File "/ragflow/rag/app/naive.py", line 436, in chunk
    sections, tables, figures = pdf_parser(filename if not binary else binary, from_page=from_page, to_page=to_page, callback=callback, separate_tables_figures=True)
  File "/ragflow/rag/app/naive.py", line 271, in __call__
    self._table_transformer_job(zoomin)
  File "/ragflow/deepdoc/parser/pdf_parser.py", line 369, in _table_transformer_job
    rows = gather(r".* (row|header)")
  File "/ragflow/deepdoc/parser/pdf_parser.py", line 364, in gather
    eles = Recognizer.layouts_cleanup(self.boxes, eles, 5, ption)
  File "/ragflow/deepdoc/vision/recognizer.py", line 152, in layouts_cleanup
    if Recognizer.overlapped_area(layouts[i], layouts[j]) < thr \
  File "/ragflow/deepdoc/vision/recognizer.py", line 126, in overlapped_area
    assert tp_ <= btm_, "Bbox mismatch! T:{},B:{},X0:{},X1:{} => {}".format(
AssertionError: Bbox mismatch! T:1954.2252909342449,B:1951.8919576009116,X0:21.90475845336914,X1:522.2380917867025 => {'x0': 21.90475845336914, 'x1': 522.2380917867025, 'top': 1945.2252909342449, 'bottom': 1968.2252909342449, 'label': 'table row', 'score': 0.7636525630950928, 'pn': 2, 'layoutno': 0}
2025-08-11 18:06:40,540 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:06:40.540+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 1, "failed": 2, "current": {}}
2025-08-11 18:07:10,542 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:07:10.542+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 1, "failed": 2, "current": {}}
2025-08-11 18:07:40,546 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:07:40.546+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 1, "failed": 2, "current": {}}
2025-08-11 18:08:10,566 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:08:10.565+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 1, "failed": 2, "current": {}}
2025-08-11 18:08:40,572 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:08:40.570+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 1, "failed": 2, "current": {}}
2025-08-11 18:09:10,575 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:09:10.575+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 1, "failed": 2, "current": {}}
2025-08-11 18:09:40,579 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:09:40.578+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 1, "failed": 2, "current": {}}
2025-08-11 18:10:10,583 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:10:10.582+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 1, "failed": 2, "current": {}}
2025-08-11 18:10:40,588 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:10:40.587+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 1, "failed": 2, "current": {}}
2025-08-11 18:11:10,590 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:11:10.590+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 1, "failed": 2, "current": {}}
2025-08-11 18:11:40,593 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:11:40.592+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 1, "failed": 2, "current": {}}
2025-08-11 18:12:10,597 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:12:10.596+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 1, "failed": 2, "current": {}}
2025-08-11 18:12:40,600 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:12:40.599+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 1, "failed": 2, "current": {}}
2025-08-11 18:13:10,604 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:13:10.603+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 1, "failed": 2, "current": {}}
2025-08-11 18:13:40,609 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:13:40.608+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 1, "failed": 2, "current": {}}
2025-08-11 18:14:10,613 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:14:10.612+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 1, "failed": 2, "current": {}}
2025-08-11 18:14:40,617 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:14:40.617+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 1, "failed": 2, "current": {}}
2025-08-11 18:15:10,619 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:15:10.619+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 1, "failed": 2, "current": {}}
2025-08-11 18:15:40,623 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:15:40.622+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 1, "failed": 2, "current": {}}
2025-08-11 18:16:10,627 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:16:10.626+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 1, "failed": 2, "current": {}}
2025-08-11 18:16:40,633 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:16:40.632+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 1, "failed": 2, "current": {}}
2025-08-11 18:17:10,637 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:17:10.636+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 1, "failed": 2, "current": {}}
2025-08-11 18:17:40,642 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:17:40.642+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 1, "failed": 2, "current": {}}
2025-08-11 18:18:10,646 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:18:10.645+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 1, "failed": 2, "current": {}}
2025-08-11 18:18:40,651 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:18:40.650+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 1, "failed": 2, "current": {}}
2025-08-11 18:19:10,655 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:19:10.654+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 1, "failed": 2, "current": {}}
2025-08-11 18:19:40,659 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:19:40.658+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 1, "failed": 2, "current": {}}
2025-08-11 18:20:10,662 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:20:10.660+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 1, "failed": 2, "current": {}}
2025-08-11 18:20:40,667 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:20:40.665+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 1, "failed": 2, "current": {}}
2025-08-11 18:21:10,674 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:21:10.672+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 1, "failed": 2, "current": {}}
2025-08-11 18:21:40,681 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:21:40.679+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 1, "failed": 2, "current": {}}
2025-08-11 18:22:10,689 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:22:10.687+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 1, "failed": 2, "current": {}}
2025-08-11 18:22:40,696 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:22:40.694+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 1, "failed": 2, "current": {}}
2025-08-11 18:23:10,704 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:23:10.702+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 1, "failed": 2, "current": {}}
2025-08-11 18:23:40,711 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:23:40.710+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 1, "failed": 2, "current": {}}
2025-08-11 18:24:10,714 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:24:10.712+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 1, "failed": 2, "current": {}}
2025-08-11 18:24:40,717 INFO     33 task_executor_5780bdf7e08c_0 reported heartbeat: {"name": "task_executor_5780bdf7e08c_0", "now": "2025-08-11T18:24:40.716+08:00", "boot_at": "2025-08-11T16:45:09.239+08:00", "pending": 0, "lag": 0, "done": 1, "failed": 2, "current": {}}
